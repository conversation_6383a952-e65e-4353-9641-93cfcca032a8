#!/usr/bin/env python3
"""
Create a focused analysis view of the parsed Wema data
"""

import pandas as pd
from datetime import datetime

def create_analysis_view():
    """Create a focused view for transaction analysis"""
    
    print("Loading parsed data...")
    df = pd.read_csv("WemaInflowDump-2025-09-16-parsed.csv")
    
    # Create a focused analysis view with key fields
    analysis_df = df[[
        'id',
        'csv_created_at',
        'originator_account_number',
        'originator_name',
        'amount_numeric',
        'currency',
        'cr_account',
        'cr_account_name',
        'bank_name',
        'bank_code',
        'payment_reference',
        'reference',
        'narration',
        'json_created_at_parsed'
    ]].copy()
    
    # Rename columns for clarity
    analysis_df.rename(columns={
        'csv_created_at': 'transaction_timestamp',
        'originator_account_number': 'sender_account',
        'originator_name': 'sender_name',
        'amount_numeric': 'amount',
        'cr_account': 'receiver_account',
        'cr_account_name': 'receiver_name',
        'json_created_at_parsed': 'original_timestamp'
    }, inplace=True)
    
    # Convert timestamps
    analysis_df['transaction_timestamp'] = pd.to_datetime(analysis_df['transaction_timestamp'])
    analysis_df['original_timestamp'] = pd.to_datetime(analysis_df['original_timestamp'])
    
    # Add derived fields for analysis
    analysis_df['transaction_date'] = analysis_df['transaction_timestamp'].dt.date
    analysis_df['transaction_hour'] = analysis_df['transaction_timestamp'].dt.hour
    analysis_df['day_of_week'] = analysis_df['transaction_timestamp'].dt.day_name()
    
    # Sort by timestamp
    analysis_df = analysis_df.sort_values('transaction_timestamp')
    
    # Save the analysis view
    output_file = "WemaInflowDump-2025-09-16-analysis.csv"
    analysis_df.to_csv(output_file, index=False)
    
    print(f"Analysis view saved to: {output_file}")
    
    # Create summary by date
    daily_summary = analysis_df.groupby('transaction_date').agg({
        'amount': ['count', 'sum', 'mean', 'min', 'max'],
        'sender_account': 'nunique',
        'receiver_account': 'nunique',
        'bank_name': 'nunique'
    }).round(2)
    
    # Flatten column names
    daily_summary.columns = ['_'.join(col).strip() for col in daily_summary.columns]
    daily_summary.rename(columns={
        'amount_count': 'transaction_count',
        'amount_sum': 'total_amount',
        'amount_mean': 'avg_amount',
        'amount_min': 'min_amount',
        'amount_max': 'max_amount',
        'sender_account_nunique': 'unique_senders',
        'receiver_account_nunique': 'unique_receivers',
        'bank_name_nunique': 'unique_banks'
    }, inplace=True)
    
    daily_summary_file = "WemaInflowDump-2025-09-16-daily-summary.csv"
    daily_summary.to_csv(daily_summary_file)
    
    print(f"Daily summary saved to: {daily_summary_file}")
    
    # Print some insights
    print("\n=== ANALYSIS INSIGHTS ===")
    print(f"Total transactions: {len(analysis_df):,}")
    print(f"Date range: {analysis_df['transaction_date'].min()} to {analysis_df['transaction_date'].max()}")
    print(f"Total amount: ₦{analysis_df['amount'].sum():,.2f}")
    print(f"Average transaction: ₦{analysis_df['amount'].mean():,.2f}")
    
    print(f"\nTop 10 transaction days:")
    top_days = daily_summary.sort_values('transaction_count', ascending=False).head(10)
    for date, row in top_days.iterrows():
        print(f"  {date}: {int(row['transaction_count']):,} transactions, ₦{row['total_amount']:,.2f}")
    
    print(f"\nTop 10 receivers by transaction count:")
    top_receivers = analysis_df['receiver_name'].value_counts().head(10)
    for name, count in top_receivers.items():
        print(f"  {name}: {count:,} transactions")
    
    print(f"\nTop 10 receivers by amount:")
    receiver_amounts = analysis_df.groupby('receiver_name')['amount'].sum().sort_values(ascending=False).head(10)
    for name, amount in receiver_amounts.items():
        print(f"  {name}: ₦{amount:,.2f}")
    
    return analysis_df, daily_summary

if __name__ == "__main__":
    create_analysis_view()

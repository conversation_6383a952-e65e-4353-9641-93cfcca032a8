#!/usr/bin/env python3
"""
Script to parse WemaInflowDump CSV file and extract JSON data into tabular format
"""

import pandas as pd
import json
import sys
from datetime import datetime

def parse_wema_data(input_file, output_file):
    """
    Parse the Wema inflow dump CSV file and extract JSON data into tabular format
    
    Args:
        input_file (str): Path to the input CSV file
        output_file (str): Path to the output CSV file
    """
    
    print(f"Reading data from {input_file}...")
    
    # Read the CSV file
    try:
        df = pd.read_csv(input_file)
        print(f"Successfully loaded {len(df)} records")
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return False
    
    # List to store parsed records
    parsed_records = []
    
    print("Parsing JSON data from 'data' column...")
    
    # Process each row
    for index, row in df.iterrows():
        try:
            # Parse the JSON data
            json_data = json.loads(row['data'])
            
            # Create a new record combining original columns with parsed JSON
            parsed_record = {
                # Original CSV columns
                'id': row['id'],
                'csv_created_at': row['created_at'],
                'csv_updated_at': row['updated_at'],
                'is_duplicate': row['is_duplicate'],
                'is_resolved': row['is_resolved'],
                'request_ip': row['request_ip'],
                'session_id': row['session_id'],
                'dumped_by': row.get('dumped_by', ''),
                
                # Parsed JSON fields
                'originator_account_number': json_data.get('originatoraccountnumber', ''),
                'amount': json_data.get('amount', ''),
                'currency': json_data.get('currency', ''),
                'originator_name': json_data.get('originatorname', ''),
                'narration': json_data.get('narration', ''),
                'cr_account_name': json_data.get('craccountname', ''),
                'payment_reference': json_data.get('paymentreference', ''),
                'reference': json_data.get('reference', ''),
                'bank_name': json_data.get('bankname', ''),
                'json_session_id': json_data.get('sessionid', ''),
                'cr_account': json_data.get('craccount', ''),
                'bank_code': json_data.get('bankcode', ''),
                'json_created_at': json_data.get('created_at', '')
            }
            
            parsed_records.append(parsed_record)
            
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON in row {index}: {e}")
            print(f"Problematic data: {row['data'][:100]}...")
            continue
        except Exception as e:
            print(f"Error processing row {index}: {e}")
            continue
    
    print(f"Successfully parsed {len(parsed_records)} records")
    
    # Create DataFrame from parsed records
    parsed_df = pd.DataFrame(parsed_records)
    
    # Convert amount to numeric for analysis
    parsed_df['amount_numeric'] = pd.to_numeric(parsed_df['amount'], errors='coerce')
    
    # Convert timestamps to datetime
    parsed_df['csv_created_at'] = pd.to_datetime(parsed_df['csv_created_at'])
    parsed_df['csv_updated_at'] = pd.to_datetime(parsed_df['csv_updated_at'])
    
    # Try to parse the JSON created_at timestamp
    def parse_json_timestamp(ts_str):
        try:
            if ts_str:
                # Handle ISO format with timezone
                return pd.to_datetime(ts_str)
        except:
            return None
    
    parsed_df['json_created_at_parsed'] = parsed_df['json_created_at'].apply(parse_json_timestamp)
    
    # Save to CSV
    print(f"Saving parsed data to {output_file}...")
    parsed_df.to_csv(output_file, index=False)
    
    # Print summary statistics
    print("\n=== SUMMARY STATISTICS ===")
    print(f"Total records processed: {len(parsed_df)}")
    print(f"Unique originator accounts: {parsed_df['originator_account_number'].nunique()}")
    print(f"Unique destination accounts: {parsed_df['cr_account'].nunique()}")
    print(f"Unique banks: {parsed_df['bank_name'].nunique()}")
    print(f"Date range (CSV): {parsed_df['csv_created_at'].min()} to {parsed_df['csv_created_at'].max()}")
    
    if parsed_df['amount_numeric'].notna().any():
        print(f"Total amount: {parsed_df['amount_numeric'].sum():,.2f} {parsed_df['currency'].iloc[0]}")
        print(f"Average amount: {parsed_df['amount_numeric'].mean():,.2f} {parsed_df['currency'].iloc[0]}")
        print(f"Amount range: {parsed_df['amount_numeric'].min():,.2f} - {parsed_df['amount_numeric'].max():,.2f}")
    
    print(f"\nTop 5 banks by transaction count:")
    print(parsed_df['bank_name'].value_counts().head())
    
    print(f"\nData saved successfully to {output_file}")
    return True

if __name__ == "__main__":
    input_file = "WemaInflowDump-2025-09-16.csv"
    output_file = "WemaInflowDump-2025-09-16-parsed.csv"
    
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    success = parse_wema_data(input_file, output_file)
    
    if success:
        print(f"\n✅ Processing completed successfully!")
        print(f"📊 Parsed data available in: {output_file}")
    else:
        print(f"\n❌ Processing failed!")
        sys.exit(1)
